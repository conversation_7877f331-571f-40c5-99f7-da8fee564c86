import request from '@/config/axios'

export const getMenuListApi = (params: any): Promise<IResponse> => {
  return request.get({ url: '/auth/menus', params })
}

export const delMenuListApi = (data: any): Promise<IResponse> => {
  return request.delete({ url: '/auth/menus', data })
}

export const addMenuListApi = (data: any): Promise<IResponse> => {
  return request.post({ url: '/auth/menus', data })
}

export const putMenuListApi = (data: any): Promise<IResponse> => {
  return request.put({ url: `/auth/menus/${data.id}`, data })
}

export const getMenuTreeOptionsApi = (): Promise<IResponse> => {
  return request.get({ url: '/auth/menus/tree/options' })
}

export const getMenuRoleTreeOptionsApi = (): Promise<IResponse> => {
  return request.get({ url: '/auth/menus/role/tree/options' })
}
