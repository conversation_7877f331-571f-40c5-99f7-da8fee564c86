import request from '@/config/axios'
import type { UserLoginType } from './types'

export const loginApi = (data: UserLoginType): Promise<IResponse> => {
  // 兼容新后端，将telephone字段映射为username
  const loginData = {
    ...data,
    username: data.telephone
  }
  return request.post({ url: '/auth/login', data: loginData })
}

export const getRoleMenusApi = (): Promise<IResponse<AppCustomRouteRecordRaw[]>> => {
  return request.get({ url: '/auth/getMenuList' })
}

export const postSMSCodeApi = (params: any): Promise<IResponse> => {
  return request.post({ url: '/system/sms/send', params })
}
