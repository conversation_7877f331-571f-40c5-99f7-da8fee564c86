import request from '@/config/axios'

export const getDeptListApi = (params: any): Promise<IResponse> => {
  return request.get({ url: '/auth/depts', params })
}

export const delDeptListApi = (data: any): Promise<IResponse> => {
  return request.delete({ url: '/auth/depts', data })
}

export const addDeptListApi = (data: any): Promise<IResponse> => {
  return request.post({ url: '/auth/depts', data })
}

export const putDeptListApi = (data: any): Promise<IResponse> => {
  return request.put({ url: `/auth/depts/${data.id}`, data })
}

export const getDeptTreeOptionsApi = (): Promise<IResponse> => {
  return request.get({ url: '/auth/dept/tree/options' })
}

export const getDeptUserTreeOptionsApi = (): Promise<IResponse> => {
  return request.get({ url: '/auth/dept/user/tree/options' })
}
