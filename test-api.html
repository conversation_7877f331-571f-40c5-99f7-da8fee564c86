<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Path Test</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            try {
                // 测试前端代理后的路径
                const response = await fetch('/api/auth/users', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.text();
                document.getElementById('result').innerHTML = `
                    <h3>Response:</h3>
                    <p>Status: ${response.status}</p>
                    <p>Data: ${data}</p>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
