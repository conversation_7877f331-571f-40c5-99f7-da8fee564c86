2025-07-21 17:19:20.734 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:19:20.734 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:21:07.795 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:21:07.796 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:21:48.171 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:21:48.171 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:24:13.957 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:24:13.957 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:25:36.196 | ERROR    | scripts.database:check_database_connection:94 - 数据库连接失败: object Row can't be used in 'await' expression
2025-07-21 17:25:36.199 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:26:53.639 | ERROR    | scripts.admin_data:init_admin_data:45 - 管理员系统数据初始化失败: (asyncmy.errors.DataError) (1406, "Data too long for column 'menu_type' at row 1")
[SQL: INSERT INTO admin_auth_menu (delete_datetime, is_delete, title, icon, path, name, component, redirect, disabled, hidden, `order`, menu_type, parent_id) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]
[parameters: (None, 0, '系统管理', 'system', '/system', 'System', 'Layout', None, 0, 0, 1, 'directory', None)]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-21 17:26:53.639 | ERROR    | scripts.init_data:init_all_data:40 - 管理员系统数据初始化失败，初始化中止
2025-07-21 17:30:17.323 | ERROR    | scripts.admin_data:init_admin_data:45 - 管理员系统数据初始化失败: 'perms' is an invalid keyword argument for AdminMenu
2025-07-21 17:30:17.324 | ERROR    | scripts.init_data:init_all_data:40 - 管理员系统数据初始化失败，初始化中止
2025-07-21 17:34:32.611 | ERROR    | scripts.admin_data:init_admin_data:45 - 管理员系统数据初始化失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT admin_auth_menu.id AS admin_auth_menu_id, admin_auth_menu.create_datetime AS admin_auth_menu_create_datetime, admin_auth_menu.update_datetime AS admin_auth_menu_update_datetime, admin_auth_menu.delete_datetime AS admin_auth_menu_delete_datetime, admin_auth_menu.is_delete AS admin_auth_menu_is_delete, admin_auth_menu.title AS admin_auth_menu_title, admin_auth_menu.icon AS admin_auth_menu_icon, admin_auth_menu.path AS admin_auth_menu_path, admin_auth_menu.name AS admin_auth_menu_name, admin_auth_menu.component AS admin_auth_menu_component, admin_auth_menu.redirect AS admin_auth_menu_redirect, admin_auth_menu.disabled AS admin_auth_menu_disabled, admin_auth_menu.hidden AS admin_auth_menu_hidden, admin_auth_menu.`order` AS admin_auth_menu_order, admin_auth_menu.menu_type AS admin_auth_menu_menu_type, admin_auth_menu.parent_id AS admin_auth_menu_parent_id 
FROM admin_auth_menu, admin_auth_role_menus 
WHERE %s = admin_auth_role_menus.role_id AND admin_auth_menu.id = admin_auth_role_menus.menu_id]
[parameters: [{'%(2459658676240 param)s': 1}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-21 17:34:32.611 | ERROR    | scripts.init_data:init_all_data:40 - 管理员系统数据初始化失败，初始化中止
2025-07-21 17:46:52.807 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D577E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D577E0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8D10C80>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:51:31.081 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C280>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C280>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C280>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C280>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D57C40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D57C40>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8D4BE00>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D64FE0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D64FE0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D64FE0>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:52:24.937 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4CF40>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4CF40>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4CF40>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4CF40>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65080>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65080>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4C290>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65620>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65620>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65620>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:52:46.130 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DBD0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DBD0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DBD0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DBD0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65120>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D653A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65120>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D653A0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4D040>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D659E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D653A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D659E0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D653A0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D659E0>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D653A0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:53:09.625 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4E9B0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4E9B0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4E9B0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4E9B0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D656C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65A80>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D656C0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65A80>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4E570>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D66020>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65A80>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D66020>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65A80>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D66020>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65A80>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:54:17.273 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DFC0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DFC0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DFC0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DFC0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D57E20>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D57E20>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8D12240>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:55:20.796 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C7C0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C7C0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C7C0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C7C0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D659E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D659E0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65940>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4D8B0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65260>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65260>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65940>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65260>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65940>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:05:41.310 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D660C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D660C0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4F740>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:09:11.124 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D662A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D662A0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E64A40>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67560>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67560>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67560>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67560>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:10:01.020 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D67600>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D67600>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E64110>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67B00>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67B00>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67B00>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67B00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:10:17.542 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4D750>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4D750>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4D750>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4D750>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8CE1DA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8CE1DA0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4DCD0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D663E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D663E0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D663E0>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:11:33.327 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65BC0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65BC0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4DC10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D662A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D662A0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D662A0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D662A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:19:43.087 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65440>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65440>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E64A10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67420>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67420>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67420>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67420>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:20:52.109 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65940>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65940>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E64C80>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D651C0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D651C0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D651C0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D651C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:22:48.378 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65D00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65D00>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E66900>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:23:45.267 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8E74400>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74720>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8E74400>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74720>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E66930>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74900>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74720>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74900>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74720>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74900>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74720>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74900>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74720>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:23:51.822 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65940>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67EC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65940>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67EC0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E66A80>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D658A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67EC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D658A0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67EC0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D658A0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67EC0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D658A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67EC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:24:03.974 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E65A20>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E65A20>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E65A20>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E65A20>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65080>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D651C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65080>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D651C0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E65430>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D659E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D651C0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D659E0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D651C0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D659E0>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D651C0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:26:59.023 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D67560>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D67560>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8D4BB00>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67C40>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67C40>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67C40>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67C40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:27:38.439 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D677E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67E20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D677E0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67E20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4E960>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57BA0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67E20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57BA0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67E20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57BA0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67E20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57BA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67E20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:46:18.832 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D57920>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57880>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D57920>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57880>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4CF80>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74180>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57880>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74180>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57880>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74180>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57880>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74180>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57880>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:46:39.934 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E806D0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E806D0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E806D0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E806D0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8E747C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74680>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8E747C0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74680>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E80140>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74680>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74680>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74680>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:49:49.389 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8E74A40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74C20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8E74A40>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74C20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E80C50>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74F40>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74C20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74F40>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74C20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74F40>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74C20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74F40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74C20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:51:53.273 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65300>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65300>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4E210>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65760>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65760>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65760>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65760>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:51:58.840 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D677E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D677E0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8D49B50>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D64F40>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D64F40>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D64F40>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D64F40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67420>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:53:23.598 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65120>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65120>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E664B0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D679C0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D679C0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D679C0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D679C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:57:08.220 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D67F60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D659E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D67F60>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D659E0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E655B0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D659E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D659E0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D659E0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D659E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:59:05.421 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8CE1D00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8CE1D00>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E650D0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74FE0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74FE0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74FE0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74FE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:59:24.266 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8E74AE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74540>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8E74AE0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74540>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E811F0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74180>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74540>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74180>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74540>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74180>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74540>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74180>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E74540>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:59:38.435 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E821D0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E821D0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E821D0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E821D0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8E74220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E749A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8E74220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E749A0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E81D90>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E754E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E749A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E754E0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E749A0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E754E0>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8E749A0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 21:15:10.470 | ERROR    | core.exception:value_exception_handler:119 - 2 validation errors for UserListOut
roles
  Error extracting attribute: StatementError: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT admin_auth_role.id AS admin_auth_role_id, admin_auth_role.create_datetime AS admin_auth_role_create_datetime, admin_auth_role.update_datetime AS admin_auth_role_update_datetime, admin_auth_role.delete_datetime AS admin_auth_role_delete_datetime, admin_auth_role.is_delete AS admin_auth_role_is_delete, admin_auth_role.name AS admin_auth_role_name, admin_auth_role.role_key AS admin_auth_role_role_key, admin_auth_role.data_range AS admin_auth_role_data_range, admin_auth_role.disabled AS admin_auth_role_disabled, admin_auth_role.`order` AS admin_auth_role_order, admin_auth_role.`desc` AS admin_auth_role_desc, admin_auth_role.is_admin AS admin_auth_role_is_admin 
FROM admin_auth_role, admin_auth_user_roles 
WHERE %s = admin_auth_user_roles.user_id AND admin_auth_role.id = admin_auth_user_roles.role_id]
[parameters: [{'%(1902086099584 param)s': 1}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s) [type=get_attribute_error, input_value=<models.admin.auth.user.A...t at 0x000001BADD4B8770>, input_type=AdminUser]
    For further information visit https://errors.pydantic.dev/2.11/v/get_attribute_error
depts
  Error extracting attribute: StatementError: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT admin_auth_dept.id AS admin_auth_dept_id, admin_auth_dept.create_datetime AS admin_auth_dept_create_datetime, admin_auth_dept.update_datetime AS admin_auth_dept_update_datetime, admin_auth_dept.delete_datetime AS admin_auth_dept_delete_datetime, admin_auth_dept.is_delete AS admin_auth_dept_is_delete, admin_auth_dept.name AS admin_auth_dept_name, admin_auth_dept.dept_key AS admin_auth_dept_dept_key, admin_auth_dept.disabled AS admin_auth_dept_disabled, admin_auth_dept.`order` AS admin_auth_dept_order, admin_auth_dept.`desc` AS admin_auth_dept_desc, admin_auth_dept.owner AS admin_auth_dept_owner, admin_auth_dept.phone AS admin_auth_dept_phone, admin_auth_dept.email AS admin_auth_dept_email, admin_auth_dept.parent_id AS admin_auth_dept_parent_id 
FROM admin_auth_dept, admin_auth_user_depts 
WHERE %s = admin_auth_user_depts.user_id AND admin_auth_dept.id = admin_auth_user_depts.dept_id]
[parameters: [{'%(1902086101216 param)s': 1}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s) [type=get_attribute_error, input_value=<models.admin.auth.user.A...t at 0x000001BADD4B8770>, input_type=AdminUser]
    For further information visit https://errors.pydantic.dev/2.11/v/get_attribute_error
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000001BAD51FEF30>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000001BAD51FEF30>
           └ <function get_command at 0x000001BAD8D0EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x000001BAD8D07EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x000001BAD8D06FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001BADBB15D90>
         │    └ <function MultiCommand.invoke at 0x000001BAD875A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000001BADCF773B0>
           │               │       │       └ <function Command.invoke at 0x000001BAD875A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x000001BADCF773B0>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x000001BADCF79EE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x000001BADCF773B0>
           │   │      │    └ <function run at 0x000001BADCF79B20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000001BAD87589A0>
           └ <click.core.Context object at 0x000001BADCF773B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x000001BADB7FDE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000001BAD878FE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BAD87C0180>
    └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BAD87C0220>
           │       │   └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
           │       └ <function run at 0x000001BAD6FCE980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BAD8748B40>
           │      └ <function Runner.run at 0x000001BAD70422A0>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BAD703BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001BAD70F7B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BAD7041BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BAD6FB5EE0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD1A5E40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A59E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000001BADD1B1B20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD1A5E40>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A59E0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BADD299790>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD1A5C60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A59E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD1A5C60>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A59E0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BADD037530>>
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD1A5C60>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A59E0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Route.handle at 0x000001BAD85625C0>
          └ APIRoute(path='/admin/auth/users', name='get_users', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD1A5C60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A59E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001BADD068860>
          └ APIRoute(path='/admin/auth/users', name='get_users', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD1A5C60>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A59E0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001BADD29B920>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001BADD1A5D00>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD2200E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A59E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001BADD1A5D00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001BADD29B920>
                     └ <function get_request_handler.<locals>.app at 0x000001BADD0687C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001BAD8560900>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'params': <apps.admin.params.user.UserParams object at 0x000001BADD298FE0>, 'paging': <core.dependencies.Paging object at 0x...
                 │         └ <function get_users at 0x000001BADBBE5440>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[Dependant(path_p...

  File "F:\share\mxtt\server\apps\admin\apis\user.py", line 29, in get_users
    return await service.get_users(params, paging)
                 │       │         │       └ <core.dependencies.Paging object at 0x000001BADD298B90>
                 │       │         └ <apps.admin.params.user.UserParams object at 0x000001BADD298FE0>
                 │       └ <function UserService.get_users at 0x000001BADBBE6520>
                 └ <apps.admin.services.user.UserService object at 0x000001BADCFD4EF0>

  File "F:\share\mxtt\server\apps\admin\services\user.py", line 64, in get_users
    datas, count = await self.user_dal.get_datas(
                         │    │        └ <function DalBase.get_datas at 0x000001BADB81BEC0>
                         │    └ <apps.admin.depts.AdminDalBase object at 0x000001BADD193F50>
                         └ <apps.admin.services.user.UserService object at 0x000001BADCFD4EF0>

  File "F:\share\mxtt\server\core\crud.py", line 192, in get_datas
    datas = [await self.out_dict(i, v_schema=v_schema) for i in result]
                   │    │                    │                  └ [<models.admin.auth.user.AdminUser object at 0x000001BADD4B8770>]
                   │    │                    └ <class 'apps.admin.schemas.user.UserListOut'>
                   │    └ <function DalBase.out_dict at 0x000001BADB834360>
                   └ <apps.admin.depts.AdminDalBase object at 0x000001BADD193F50>

  File "F:\share\mxtt\server\core\crud.py", line 333, in out_dict
    return v_schema.model_validate(obj).model_dump()
           │        │              └ <models.admin.auth.user.AdminUser object at 0x000001BADD4B8770>
           │        └ <classmethod(<function BaseModel.model_validate at 0x000001BAD78C9760>)>
           └ <class 'apps.admin.schemas.user.UserListOut'>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           │   │                      └ <method 'validate_python' of 'pydantic_core._pydantic_core.SchemaValidator' objects>
           │   └ SchemaValidator(title="UserListOut", validator=Model(
           │         ModelValidator {
           │             revalidate: Never,
           │             validator: Mode...
           └ <class 'apps.admin.schemas.user.UserListOut'>

pydantic_core._pydantic_core.ValidationError: 2 validation errors for UserListOut
roles
  Error extracting attribute: StatementError: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT admin_auth_role.id AS admin_auth_role_id, admin_auth_role.create_datetime AS admin_auth_role_create_datetime, admin_auth_role.update_datetime AS admin_auth_role_update_datetime, admin_auth_role.delete_datetime AS admin_auth_role_delete_datetime, admin_auth_role.is_delete AS admin_auth_role_is_delete, admin_auth_role.name AS admin_auth_role_name, admin_auth_role.role_key AS admin_auth_role_role_key, admin_auth_role.data_range AS admin_auth_role_data_range, admin_auth_role.disabled AS admin_auth_role_disabled, admin_auth_role.`order` AS admin_auth_role_order, admin_auth_role.`desc` AS admin_auth_role_desc, admin_auth_role.is_admin AS admin_auth_role_is_admin 
FROM admin_auth_role, admin_auth_user_roles 
WHERE %s = admin_auth_user_roles.user_id AND admin_auth_role.id = admin_auth_user_roles.role_id]
[parameters: [{'%(1902086099584 param)s': 1}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s) [type=get_attribute_error, input_value=<models.admin.auth.user.A...t at 0x000001BADD4B8770>, input_type=AdminUser]
    For further information visit https://errors.pydantic.dev/2.11/v/get_attribute_error
depts
  Error extracting attribute: StatementError: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT admin_auth_dept.id AS admin_auth_dept_id, admin_auth_dept.create_datetime AS admin_auth_dept_create_datetime, admin_auth_dept.update_datetime AS admin_auth_dept_update_datetime, admin_auth_dept.delete_datetime AS admin_auth_dept_delete_datetime, admin_auth_dept.is_delete AS admin_auth_dept_is_delete, admin_auth_dept.name AS admin_auth_dept_name, admin_auth_dept.dept_key AS admin_auth_dept_dept_key, admin_auth_dept.disabled AS admin_auth_dept_disabled, admin_auth_dept.`order` AS admin_auth_dept_order, admin_auth_dept.`desc` AS admin_auth_dept_desc, admin_auth_dept.owner AS admin_auth_dept_owner, admin_auth_dept.phone AS admin_auth_dept_phone, admin_auth_dept.email AS admin_auth_dept_email, admin_auth_dept.parent_id AS admin_auth_dept_parent_id 
FROM admin_auth_dept, admin_auth_user_depts 
WHERE %s = admin_auth_user_depts.user_id AND admin_auth_dept.id = admin_auth_user_depts.dept_id]
[parameters: [{'%(1902086101216 param)s': 1}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s) [type=get_attribute_error, input_value=<models.admin.auth.user.A...t at 0x000001BADD4B8770>, input_type=AdminUser]
    For further information visit https://errors.pydantic.dev/2.11/v/get_attribute_error
2025-07-21 21:15:19.054 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000001BAD51FEF30>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000001BAD51FEF30>
           └ <function get_command at 0x000001BAD8D0EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x000001BAD8D07EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x000001BAD8D06FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001BADBB15D90>
         │    └ <function MultiCommand.invoke at 0x000001BAD875A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000001BADCF773B0>
           │               │       │       └ <function Command.invoke at 0x000001BAD875A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x000001BADCF773B0>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x000001BADCF79EE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x000001BADCF773B0>
           │   │      │    └ <function run at 0x000001BADCF79B20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000001BAD87589A0>
           └ <click.core.Context object at 0x000001BADCF773B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x000001BADB7FDE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000001BAD878FE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BAD87C0180>
    └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BAD87C0220>
           │       │   └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
           │       └ <function run at 0x000001BAD6FCE980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BAD8748B40>
           │      └ <function Runner.run at 0x000001BAD70422A0>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BAD703BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001BAD70F7B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BAD7041BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BAD6FB5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x000001BADD3225C0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x000001BADD3225C0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x000001BADD3225C0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x000001BADD3225C0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD220C20>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD201620>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000001BADD1B1B20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD220C20>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD201620>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BADD321D90>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB600>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD201620>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB600>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD201620>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BADD037530>>
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB600>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD201620>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x000001BADD037530>>
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 21:16:09.327 | ERROR    | core.exception:validation_exception_handler:85 - [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': {}}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': {}}]
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000001BAD51FEF30>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000001BAD51FEF30>
           └ <function get_command at 0x000001BAD8D0EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x000001BAD8D07EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x000001BAD8D06FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001BADBB15D90>
         │    └ <function MultiCommand.invoke at 0x000001BAD875A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000001BADCF773B0>
           │               │       │       └ <function Command.invoke at 0x000001BAD875A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x000001BADCF773B0>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x000001BADCF79EE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x000001BADCF773B0>
           │   │      │    └ <function run at 0x000001BADCF79B20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000001BAD87589A0>
           └ <click.core.Context object at 0x000001BADCF773B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x000001BADB7FDE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000001BAD878FE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BAD87C0180>
    └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BAD87C0220>
           │       │   └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
           │       └ <function run at 0x000001BAD6FCE980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BAD8748B40>
           │      └ <function Runner.run at 0x000001BAD70422A0>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BAD703BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001BAD70F7B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BAD7041BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BAD6FB5EE0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD5FB4C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213F60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000001BADD1B1B20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD5FB4C0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213F60>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BADD323170>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB880>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213F60>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB880>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213F60>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BADD037530>>
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB880>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213F60>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Route.handle at 0x000001BAD85625C0>
          └ APIRoute(path='/admin/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB880>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213F60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001BADD053560>
          └ APIRoute(path='/admin/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB880>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213F60>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001BADD323800>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001BADD5FB920>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FBA60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213F60>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001BADD5FB920>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001BADD323800>
                     └ <function get_request_handler.<locals>.app at 0x000001BADD052DE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py", line 346, in app
    raise validation_error
          └ RequestValidationError([{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': {}}, {'type': 'mis...

fastapi.exceptions.RequestValidationError: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': {}}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': {}}]
2025-07-21 21:24:49.283 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000001BAD51FEF30>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000001BAD51FEF30>
           └ <function get_command at 0x000001BAD8D0EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x000001BAD8D07EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x000001BAD8D06FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001BADBB15D90>
         │    └ <function MultiCommand.invoke at 0x000001BAD875A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000001BADCF773B0>
           │               │       │       └ <function Command.invoke at 0x000001BAD875A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x000001BADCF773B0>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x000001BADCF79EE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x000001BADCF773B0>
           │   │      │    └ <function run at 0x000001BADCF79B20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000001BAD87589A0>
           └ <click.core.Context object at 0x000001BADCF773B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x000001BADB7FDE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000001BAD878FE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BAD87C0180>
    └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BAD87C0220>
           │       │   └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
           │       └ <function run at 0x000001BAD6FCE980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BAD8748B40>
           │      └ <function Runner.run at 0x000001BAD70422A0>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BAD703BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001BAD70F7B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BAD7041BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BAD6FB5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD5FB880>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD5FBA60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000001BADD1B1B20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD5FB880>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD5FBA60>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BADD3231A0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB420>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD5FBA60>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB420>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD5FBA60>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BADD037530>>
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB420>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD5FBA60>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x000001BAD8562E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000001BADD037500>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD5FB420>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD5FBA60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000001BADD037500>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000001BADD037500>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000001BAD882CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x000001BADD037500>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 21:24:55.471 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000001BAD51FEF30>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000001BAD51FEF30>
           └ <function get_command at 0x000001BAD8D0EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x000001BAD8D07EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x000001BAD8D06FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001BADBB15D90>
         │    └ <function MultiCommand.invoke at 0x000001BAD875A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000001BADCF773B0>
           │               │       │       └ <function Command.invoke at 0x000001BAD875A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x000001BADCF773B0>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x000001BADCF79EE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x000001BADCF773B0>
           │   │      │    └ <function run at 0x000001BADCF79B20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000001BAD87589A0>
           └ <click.core.Context object at 0x000001BADCF773B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x000001BADB7FDE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000001BAD878FE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BAD87C0180>
    └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BAD87C0220>
           │       │   └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
           │       └ <function run at 0x000001BAD6FCE980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BAD8748B40>
           │      └ <function Runner.run at 0x000001BAD70422A0>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BAD703BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001BAD70F7B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BAD7041BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BAD6FB5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD213920>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213CE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000001BADD1B1B20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD213920>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213CE0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BADD493B60>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD213C40>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213CE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD213C40>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213CE0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BADD037530>>
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD213C40>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213CE0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x000001BAD8562E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000001BADD037500>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD213C40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD213CE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000001BADD037500>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000001BADD037500>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x000001BAD882CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x000001BADD037500>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 21:25:13.459 | ERROR    | core.exception:validation_exception_handler:85 - [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': {'telephone': '***********', 'password': 'admin123', 'method': '0', 'platform': '0'}}]
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000001BAD51FEF30>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000001BAD51FEF30>
           └ <function get_command at 0x000001BAD8D0EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x000001BAD8D07EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x000001BAD8D06FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001BADBB15D90>
         │    └ <function MultiCommand.invoke at 0x000001BAD875A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000001BADCF773B0>
           │               │       │       └ <function Command.invoke at 0x000001BAD875A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x000001BADCF773B0>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x000001BADCF79EE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x000001BADCF773B0>
           │   │      │    └ <function run at 0x000001BADCF79B20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000001BAD87589A0>
           └ <click.core.Context object at 0x000001BADCF773B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x000001BADB7FDE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000001BAD878FE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BAD87C0180>
    └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BAD87C0220>
           │       │   └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
           │       └ <function run at 0x000001BAD6FCE980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BAD8748B40>
           │      └ <function Runner.run at 0x000001BAD70422A0>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BAD703BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001BAD70F7B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BAD7041BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BAD6FB5EE0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD1A5EE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A5E40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000001BADD1B1B20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD1A5EE0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A5E40>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BADD299760>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD220C20>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A5E40>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD220C20>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A5E40>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BADD037530>>
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD220C20>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A5E40>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Route.handle at 0x000001BAD85625C0>
          └ APIRoute(path='/admin/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD220C20>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A5E40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001BADD053560>
          └ APIRoute(path='/admin/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD220C20>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A5E40>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001BADD29A990>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001BADD220400>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD220D60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD1A5E40>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001BADD220400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001BADD29A990>
                     └ <function get_request_handler.<locals>.app at 0x000001BADD052DE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py", line 346, in app
    raise validation_error
          └ RequestValidationError([{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': {'telephone': '138...

fastapi.exceptions.RequestValidationError: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': {'telephone': '***********', 'password': 'admin123', 'method': '0', 'platform': '0'}}]
2025-07-21 21:26:09.581 | ERROR    | core.exception:validation_exception_handler:85 - [{'type': 'json_invalid', 'loc': ('body', 1), 'msg': 'JSON decode error', 'input': {}, 'ctx': {'error': 'Expecting property name enclosed in double quotes'}}]
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py", line 262, in app
    json_body = await request.json()
                      │       └ <function Request.json at 0x000001BAD84DE840>
                      └ <starlette.requests.Request object at 0x000001BADD625A00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
    │            │    │     └ b'{\\'
    │            │    └ <function loads at 0x000001BAD7604F40>
    │            └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>
    └ <starlette.requests.Request object at 0x000001BADD625A00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           │                │      └ '{\\'
           │                └ <function JSONDecoder.decode at 0x000001BAD7604900>
           └ <json.decoder.JSONDecoder object at 0x000001BAD75F2330>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\decoder.py", line 338, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               │    │          │      │  └ '{\\'
               │    │          │      └ <built-in method match of re.Pattern object at 0x000001BAD75831D0>
               │    │          └ '{\\'
               │    └ <function JSONDecoder.raw_decode at 0x000001BAD76049A0>
               └ <json.decoder.JSONDecoder object at 0x000001BAD75F2330>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\decoder.py", line 354, in raw_decode
    obj, end = self.scan_once(s, idx)
               │    │         │  └ 0
               │    │         └ '{\\'
               │    └ <_json.Scanner object at 0x000001BAD75CFA60>
               └ <json.decoder.JSONDecoder object at 0x000001BAD75F2330>

json.decoder.JSONDecodeError: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000001BAD51FEF30>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000001BAD51FEF30>
           └ <function get_command at 0x000001BAD8D0EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x000001BAD8D07EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x000001BAD8D06FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001BADBB15D90>
         │    └ <function MultiCommand.invoke at 0x000001BAD875A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000001BADCF773B0>
           │               │       │       └ <function Command.invoke at 0x000001BAD875A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x000001BADCF773B0>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x000001BADCF79EE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x000001BADCF773B0>
           │   │      │    └ <function run at 0x000001BADCF79B20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000001BAD87589A0>
           └ <click.core.Context object at 0x000001BADCF773B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x000001BADB7FDE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000001BAD878FE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BAD87C0180>
    └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BAD87C0220>
           │       │   └ <uvicorn.server.Server object at 0x000001BADCFB44A0>
           │       └ <function run at 0x000001BAD6FCE980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BAD8748B40>
           │      └ <function Runner.run at 0x000001BAD70422A0>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BAD703BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BADCFB4440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001BAD70F7B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BAD7041BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BAD6FB5EE0>
    └ <Handle Task.task_wakeup(<Future cancelled>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future cancelled>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD620900>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD620040>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000001BADD1B1B20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BADD620900>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD620040>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BADD6253A0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BADD1B1D90>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD620AE0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD620040>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD620AE0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD620040>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BADD037530>>
          └ <fastapi.routing.APIRouter object at 0x000001BADD037530>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD620AE0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD620040>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Route.handle at 0x000001BAD85625C0>
          └ APIRoute(path='/admin/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD620AE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD620040>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001BADD053560>
          └ APIRoute(path='/admin/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD620AE0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD620040>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001BADD625A00>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001BADD620B80>
          └ <function wrap_app_handling_exceptions at 0x000001BAD8561080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BADD620CC0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BADD620040>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001BADD620B80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001BADD625A00>
                     └ <function get_request_handler.<locals>.app at 0x000001BADD052DE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py", line 280, in app
    raise validation_error from e
          └ RequestValidationError([{'type': 'json_invalid', 'loc': ('body', 1), 'msg': 'JSON decode error', 'input': {}, 'ctx': {'error'...

fastapi.exceptions.RequestValidationError: [{'type': 'json_invalid', 'loc': ('body', 1), 'msg': 'JSON decode error', 'input': {}, 'ctx': {'error': 'Expecting property name enclosed in double quotes'}}]
